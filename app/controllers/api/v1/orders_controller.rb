class Api::V1::OrdersController < ApiController
  include Api::V1::OrderHelper

  NON_SERVICABLE = LOCKDOWN_NON_SERVICABLE_PINCODES
  
  skip_before_filter :validate_headers?, :currency, only: [:payu_response, :paypal_success, :paypal_capture, :paypal_cancel, :generate_paytm_checksum, :verify_paytm_checksum, :braintree_checkout, :stripe_response]
  before_action :authenticate_api_account!, :authenticate_user!, except: [:payu_response, :paypal_capture,:paypal_success, :paypal_cancel, :generate_paytm_checksum, :verify_paytm_checksum, :braintree_checkout, :stripe_checkout, :stripe_response]
  before_action :validate_min_order_value, only: [:create]
  before_action :validate_pincode_deliverable, only: [:create]
  before_action :validate_wallet_payment_method, only: [:create]
  before_action :set_response_header, only: [:create]
  DESIGN_ORDER_RETURN_DAYS = SystemConstant.get('DESIGN_ORDER_RETURN_DAYS').to_i

  # Create order
  #
  # POST /user/orders
  #
  def create
    order = current_user.orders.new(cart_id: current_cart.id)
    if order_params[:shipping_address].present?
      order.build_address_by_address_id(order_params[:shipping_address], 'shipping')
    end
    if is_banned_country([order.country.try(:downcase), order.billing_country.try(:downcase), @actual_country.try(:downcase)])
      msg = BANNED_COUNTRIES['reason']
      render json: {errors: msg }, status: 422 and return
    end
    if (msg = validate_cart(((order_params[:pay_type].to_s == Order::COD || (order.country.try(:downcase) == @actual_country.try(:downcase) && @actual_country.try(:downcase) == 'india')) ? 'india' : 'international'))).present?
      render json: { errors: msg }, status: 422 and return
    end
    order.app_source = "#{@app_source}-#{request.headers['App-Version']}"
    order.ip_address = (request.headers['True-Client-IP'].presence || request.remote_ip.presence || request.ip)
    order.other_details['user_agent'] = request.user_agent
    order.other_details['session_id'] = session.id
    order.add_notes('quick_cod_order', false) if params[:order][:quick_cod_request] == 'true'
    order.assign_attributes(get_utm_params) if params[:order][:utm].present?
    order.assign_warehouse_addresses_to_designer_orders
    order.set_subscription_details(subscription_params.merge(app_source: @app_source), @rate, @country_code) if subscription_params.present?
    if order_params[:prepaid_discount].to_i > 0
      if (amount = (@cart.prepaid_discount).to_i) > 0
        order.order_discounts.new(discount_type: 'prepaid', amount: amount)
      end
    end
    if params[:mastercard_discount].to_f > 0 && (amount = (@cart.mastercard_discount(@country_code, @rate)).to_i) > 0
      @order.order_discounts.new(discount_type: 'mastercard', amount: amount)
    end
    order.build_by(order_params.except(:prepaid_discount).merge!({
      country_code: @country_code, rate: @rate, symbol: @symbol}
    )).save
    if SOR_READY_TO_SHIP && current_cart.warehouse_available_designs? #ApplicationController.helpers.rts_available_country?(order.country)
      order.express_delivery = 0
      order.add_notes_without_callback('Ready_To_Ship_Order_SOR', 'delivery')
    end
    Order.sidekiq_delay_until(30.minutes.from_now, queue: 'high').cancel_unless_confirmed_mobile(order.id) if order.cancel_unless_confirmed?
    order.do_wallet_transaction
    # Order.delay(queue: 'high_priority', priority: 0).generate_thirdwatch_payload(session.id, request.ip, request.user_agent, order.id) if Rails.env.production? || Rails.env.staging?
    Order.sidekiq_delay.update_paypal_rate_mobile(order.id) if order.international?
    methods = paytype_response_methods(order.pay_type, order.billing_country, order.total, order_params[:is_stripe])
    if (errors = order.errors).any?
      Order.sidekiq_delay.notify_exceptions("Order validation notification", "lost order due to validation", { order: order.inspect,
        validation_errors: errors,
        designer_orders: order.designer_orders,
        cart: order.cart.inspect,
        line_item: order.cart.try(:line_items).inspect,
        params: params
      })
      if errors[:designer_orders].present?
        render json: { errors: 'The product/s in your cart has gone out of stock, please verify and try again.' }, status: 422 and return
      end
    end
    response_for order, methods
    rescue Paypal::Exception::APIError => error
      order.add_log_details(error)
      render json: { errors: 'Payment Failed!Please Try Again'},status: 422 and return
  end

  # Provides the order list of current_user
  #
  # GET /user/orders/confirmed                  For confirmed orders
  # GET /user/orders/canceled                   For cancelled orders
  # GET /user/orders/payment_pending            For pending orders
  # GET /user/orders                            For all orders
  #
  # == Returns
  # JSON
  #
  def index
    @orders = Kaminari.paginate_array(
      current_user.orders.includes(designer_orders: [line_items: [:line_item_addons]]).order('created_at desc').non_zero_orders
    ).page(params[:page]).per(20)
    if @orders.blank?
      head :no_content
    else
      render :index
    end
  end

  def confirmed
    states = 'confirmed', 'sane', 'pickedup', 'dispatched', 'ready_for_dispatch', 'partial_dispatch', 'complete'
    @orders = Kaminari.paginate_array(
      current_user.orders.where(state: states).includes(designer_orders: [line_items: [:line_item_addons]]).order('created_at desc').non_zero_orders
    ).page(params[:page]).per(20)
    head :no_content and return if @orders.blank?
  end

  def canceled
    states = 'cancel_complete', 'cancel', 'reject'
    @orders = Kaminari.paginate_array(
      current_user.orders.where(state: states).includes(designer_orders: [line_items: [:line_item_addons]]).order('created_at desc').non_zero_orders
    ).page(params[:page]).per(20)
    head :no_content and return if @orders.blank?
  end

  def payment_pending
    payment_states = 'pending_payment', 'void', 'failed', 'processing'
    order_states = 'cancel_complete', 'cancel', 'reject'
    @orders = Kaminari.paginate_array(
      current_user.orders.where(payment_state: payment_states).where.not(state: order_states).includes(designer_orders: [line_items: [:line_item_addons]]).order('created_at desc').non_zero_orders
    ).page(params[:page]).per(20)
    head :no_content and return if @orders.blank?
  end

  # Provides the order of current_user
  #
  # GET /user/orders/:id
  #
  # == Returns
  # JSON
  #
  def show
    @order = current_user.orders.includes(designer_orders: [line_items: [design: [:designer,
       :master_image],line_item_addons: :addon_type_value]]).find_by_id(params[:id])
    head :no_content and return if @order.blank?
    @item_status = @order.returnable_line_items(@app_source,request.headers['App-Version'])
    @returnable_line_items_ids = @item_status.select { |k,v| v == '' }.keys
    @order_status = @order.order_status_api
    # if @order.present? && @order.cod? && @order.state=='pending' && @order.cod_verification_attempt_pending?
    #   @order.send_cod_verification(orders_cod_verify_url)
    # end
  end

  # Error here are handled on desktop for method paypal_update_order
  def paypal_verify
    order = Order.find_by_id(params[:order_id])
    if order.present? && params[:paypal_txn_id].present?
      if Order.where(paypal_txn_id: params[:paypal_txn_id]).exists?
        Order.sidekiq_delay.notify_exceptions("Paypal Error: Same PAY-txn id", 'PAY-txn id from app already used', { order: order.number.to_s, params: params.to_json })
        render json: {paypal_txn_id: 'already used', errors: ['Something went wrong, Please contact customer care.']},
          status: :unprocessable_entity
      else
        order.paypal_txn_id = params[:paypal_txn_id]
        order.save
        if order.cart.present?
          order.cart.update_column(:used, true)
          #order.wallet_deductions(order.cart)
        end
        Order.sidekiq_delay.paypal_update_order(order.id)
        head :ok
      end
    else
      if order.present?
        Order.sidekiq_delay.notify_exceptions("Paypal Error: No PAY-txn id", 'No PAY-txn id from app', { order: order.number.to_s, params: params.to_json })
        order.update_column(:payu_error, 'PPNOPAYTXNID')
      else
        Order.sidekiq_delay.notify_exceptions("Paypal Error: No order found", 'No order found from app', params.to_json)
      end
      head :no_content
    end
  end

  # Receives response from payu for succecss, cancel and errors case.
  #
  # POST /api/v1/payu_response
  #
  # == Returns
  # HTML Response
  #
  def payu_response
    if Order.valid_payu_response_checksum?( params[:hash], params)
      if (@order = Order.find_by(number: params[:txnid]))
        @order.assign_payu_attributes(params)
        @order.save
        if @order.cart.present?
          @order.cart.update_column(:used, true)
          #@order.wallet_deductions(@order.cart)
        end
        Order.sidekiq_delay.process_payu_order!(@order.id, params[:amount])
      end
    end
    render layout: false
  end

  def juspay_response
    render layout: false
  end

  def paypal_success
    @order = Order.find_by_token! params[:token]
    response = @order.complete!(params[:PayerID])
    if response.try(:ack) == 'Success'
      @response = response.ack
      @order.paypal_update_cc_order(response)
    else
      @order.update_column(:ccavenue_api_error, 'PPRESERROR_API')
      Order.sidekiq_delay.notify_exceptions("Paypal Response Error API", 'Problem in processing the payment', { order: @order.number.to_s, response: response.inspect.to_s })
    end
    render layout: false
  end

  def paypal_capture
    render json: {has_error: true, message: "Some Exception Occured Please Try Again"} and return if params[:orderID].blank?
    begin      
      order = Order.find_by_token params[:orderID] 
      payapal_api = Payment::Paypal::PaypalV2RestApi.new(order)
      payment_response = payapal_api.capture_payment
      if payment_response.key?("has_error") 
        order.update_column("paypal_error", [payment_response['name'], payment_response['message'], payment_response['errors']].compact.join(' || '))
      else
        order.save
        order.cart.update_column(:used, true)
        Order.sidekiq_delay.store_paypal_smartpay_v2_api_details(payment_response, order.number)
      end
      payment_response = payment_response.merge({order_details: order.order_details})
      render json: payment_response
    rescue => error
      Order.sidekiq_delay.notify_exceptions("Paypal Rutime Exception", error.inspect, {order_number: session[:order_number]})
      Order.where(number: session[:order_number]).update_all(paypal_error: error.inspect)
      render json: {has_error: true, message: "Some Exception Occured Please Try Again"}
    end
  end

  def paypal_cancel
    @response = 'Failed'
    @order = Order.find_by_token! params[:token]
    if @order.present?
      @order.update_column(:ccavenue_api_error, 'PPUSERCANCEL_API')
      Order.sidekiq_delay.notify_exceptions("Paypal Cancel", 'Paypal request cancel api', { order: @order.number.to_s })
    end
    render layout: false
  end

  def stripe_checkout
    begin
      order = Order.find_by_number(params[:number])
      order.update_column(:attempted_payment_gateway, 'stripe')
      s = Stripe::StripeService.new(order)
      intent = s.create_intent
      if intent[:error].present?
        render json: intent
      else
        render json: {paymentIntent: intent[:client_secret], publishableKey: ENV['STRIPE_PUBLISHABLE_KEY'], customer: intent[:customer_id], ephemeralKey: intent[:ephemeral_secret]}
      end
    rescue => e
      render json: {error: "Invalid Request", status: 400}
    end
  end

  def stripe_response
    payload = request.body.read
    sig_header = request.env['HTTP_STRIPE_SIGNATURE']
    endpoint_secret = Rails.configuration.stripe[:stripe_endpoint_sceret]
    event = nil
    begin
      event = Stripe::Webhook.construct_event(
        payload, sig_header, endpoint_secret
      )
    rescue JSON::ParserError => e
        # Invalid payload
      Order.sidekiq_delay.notify_exceptions("Stripe Exception", "Invalid Paylaod")
      render json: {error: "Invalid Request", status: 400} and return 
    rescue Stripe::SignatureVerificationError => e
        # Invalid signature
      Order.sidekiq_delay.notify_exceptions("Stripe Exception", "Verification Failed")
      render json: {error: "Invalid Request", status: 400} and return 
    end
    stripe_txn = PaymentGatewayTransaction.find_by_transaction_id(event.data.object.id)
    stripe_txn.update_attributes({pay_type: event.data.object.payment_method_types.first, error: event.data.object.last_payment_error})
    order = stripe_txn.order
    order.update_attribute('payment_gateway','Stripe')
    # Handle the event
    case event.type
    when 'payment_intent.canceled'
      stripe_txn.payment_canceled
    when 'payment_intent.created'
      puts "Payment Created"
    when 'payment_intent.payment_failed'
      stripe_txn.payment_failed
    when 'payment_intent.processing'
      stripe_txn.in_processing
    when 'payment_intent.succeeded'
      stripe_txn.payment_success
    else
      Order.sidekiq_delay.notify_exceptions("Stripe Exception", "Unhandled event type: #{event.type}", {order_number: order.number})
    end
    head :ok
  end

  def get_cod_charge
    cart = Cart.find_by_id(params[:cart_id])
    return Order.domestic_cod_charge(params[:pincode], cart)
  end

  def generate_paytm_checksum
    paytm_keys = %w(REQUEST_TYPE MID ORDER_ID CUST_ID TXN_AMOUNT CHANNEL_ID INDUSTRY_TYPE_ID WEBSITE MOBILE_NO EMAIL MSISDN CALLBACK_URL THEME SUBS_SERVICE_ID SUBS_AMOUNT_TYPE SUBS_FREQUENCY SUBS_FREQUENCY_UNIT SUBS_ENABLE_RETRY SUBS_EXPIRY_DATE PAYMENT_MODE_ONLY AUTH_MODE PAYMENT_TYPE_ID CARD_TYPE BANK_CODE PROMO_CAMP_ID SUBS_MAX_AMOUNT SUBS_START_DATE SUBS_GRACE_DAYS SUBS_ID ORDERID REFUNDAMOUNT TXNTYPE REFID TXNID COMMENTS MERC_UNQ_REF)
    render json: Order.generate_checsum(params.to_hash.slice(*paytm_keys))
  end

  def verify_paytm_checksum
    paytm_keys = %w(SUBS_ID MID BANKTXNID TXNAMOUNT CURRENCY STATUS RESPCODE RESPMSG TXNDATE GATEWAYNAME BANKNAME PAYMENTMODE PROMO_CAMP_ID PROMO_STATUS PROMO_RESPCODE ORDERID TXNID REFUNDAMOUNT REFID MERC_UNQ_REF CHECKSUMHASH)
    @verification_array = Order.verify_checksum_array(params.to_hash.slice(*paytm_keys))
    if @verification_array["IS_CHECKSUM_VALID"] == 'Y' && (order = Order.find_by_number(@verification_array['ORDERID']))
      if (order.paytm_txn_id.nil? || order.paytm_txn_id != @verification_array['TXNID'])
        order.assign_paytm_attributes(@verification_array)
        order.save
        #order.wallet_deductions(order.cart) if order.cart.present?
        Order.sidekiq_delay.process_paytm_order!(order.id, @verification_array['TXNAMOUNT'])
      else
        (order.payment_gateway_details ||= "" ) << "\n" << @verification_array.to_json
        order.save
      end
    end
    render layout: false
  end

  def validate_min_order_value
    shipping_country = Address.find_by_id(order_params[:shipping_address]).try(:country)
    min_cart_value = INTERNATIONAL_MIN_CART_VAL.to_i #(min = CurrencyConvert.find_by_country_code(@country_code).try(:min_cart_value).to_i).nonzero? ? min : INTERNATIONAL_MIN_CART_VAL.to_i
    if current_cart.check_for_min_cart_condition?(MIN_CART_VALUE_ON_HEADER[@app_source.downcase], @country_code, shipping_country) && (current_cart.item_total(1).to_i - current_cart.additional_discounts(@actual_country)) < min_cart_value
      render json: {
        errors: "International Orders should have a minimum order value of #{@symbol} #{CurrencyConvert.to_currency(@country_code, min_cart_value).round(2)} (shipping charges excluded)."
        }, status: 422 and return
    end
  end

  def validate_pincode_deliverable
    shipping_address = Address.find_by_id(order_params[:shipping_address])
    shipping_pincode = shipping_address.try(:pincode)
    if NON_SERVICABLE.include?shipping_pincode
      render json: {
        errors: "We are currently not delivering to this location."
        }, status: 422 and return
    end
    if shipping_address.try(:country).try(:downcase).to_s == 'india' && !@cart.pincode_servicable_for_category?(shipping_pincode)
      render json: {
        errors: PINCODE_BASED_CATEGORY_DELIVERY['reason']
        }, status: 422 and return
    end
  end

  def braintree_client_token
    render json: {client_token: Order.generate_client_token}
  end

  def braintree_checkout
    if (order = Order.find_by_id(params[:order_id])).present? && params[:nonce].present? && params[:device_data].present?
      result = order.create_braintree_paypal_transaction(params[:nonce], params[:device_data])
      if result.success?
        order.process_braintree_order(result, params[:device_data])
        if (cart = order.cart).present?
          cart.update_column(:used, true)
        end
        head :ok
      else
        order.paypal_error = order.get_error_info(result)
        order.store_braintree_related_data(result.try(:transaction), params[:device_data])
        render json: { errors: "Payment Failed! #{result.try(:message)}" }, status: 422
      end
      Order.sidekiq_delay.cancel_unless_confirmed_mobile(order.id)
    else
      Order.sidekiq_delay.notify_exceptions("Braintree Error: Params missing", 'Required parameters are missing', params.to_json)
      head :bad_request
    end
  end

  private

  # Required params for create
  #
  def order_params
    params.require(:order).permit(:shipping_address, :billing_address, :pay_type, :prepaid_discount, :is_stripe)
  end

  def subscription_params
    params.fetch(:subscription, {}).permit(:subscription_applied, :subscription_plan_id)
  end

  def get_utm_params
    params.require(:order).require(:utm).permit(:utm_source, :utm_medium, :utm_campaign, :utm_term, :utm_content)
  end

  def is_banned_country(countries = [])
    (BANNED_COUNTRIES['countries'] & countries).present?
  end

  def validate_cart(country)
    if current_cart.line_items.blank?
      return "Oops! Your cart seems empty. Please add items and try again. Alternately, check 'My Orders' section."
    elsif current_cart.check_out_of_stock?(country)
      return 'The product/s in your cart has gone out of stock, please verify and try again.'
    end
  end

  def validate_wallet_payment_method
    render json: { errors: 'Wallet cannot be used with Cash on Delivery, please select other payment option.' }, status: 422 and return if params[:order][:pay_type] == 'Cash On Delivery' && current_cart.wallet_discount_applied?
  end

end
