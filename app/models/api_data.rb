class ApiData < ActiveRecord::Base
  belongs_to :ac
  belongs_to :currency_convert
  has_one :api_notification_settings
  validates_uniqueness_of :device_id, scope: :account_id

  def update_data(curr_device_id, curr_app_source, curr_app_version, country)
    self.device_id = curr_device_id unless self.device_id.eql?(curr_device_id)
    self.currency_convert_id = country.id unless self.currency_convert_id.eql?(country.id)
    self.app_source = curr_app_source unless curr_app_source.nil? || self.app_source.eql?(curr_app_source)
    self.app_version = curr_app_version unless curr_app_version.nil? || self.app_version.eql?(curr_app_version)
    self.save if self.changed?
  end

  def update_wishlist_privacy(value)
    self.update(wishlist_public: to_bool(value))
    return self
  end

  private

  def to_bool(value)
    value == "true"
  end

end